 {% comment %}
  Key Features Section for Ideaformer IR3 V2 3D Printer
  File: sections/ir3-v2-key-features.liquid
{% endcomment %}

{{ 'ir3-v2-key-features.css' | asset_url | stylesheet_tag }}

<section
  class="key-features-section"
  id="key-features-{{ section.id }}"
  data-section-id="{{ section.id }}"
  style="margin-top: 0px; margin-bottom: {{ section.settings.margin_bottom }}px;"
>
  <!-- 背景图层 -->
  <div class="background-layer">
    <div class="gradient-overlay"></div>
    <div class="grid-pattern"></div>
    <div class="tech-lines"></div>
    <div class="floating-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
    </div>
  </div>

  <!-- Dynamic 3D Printing Elements -->
  <div class="printing-particles"></div>
  <div class="tech-circuits">
    <div class="circuit circuit-1"></div>
    <div class="circuit circuit-2"></div>
    <div class="circuit circuit-3"></div>
  </div>
  <div class="floating-icons">
    <div class="print-icon icon-1">⚙️</div>
    <div class="print-icon icon-2">🔧</div>
    <div class="print-icon icon-3">📐</div>
    <div class="print-icon icon-4">🎯</div>
  </div>
  <div class="energy-beams">
    <div class="beam beam-1"></div>
    <div class="beam beam-2"></div>
  </div>

  <!-- Content Container -->
  <div class="features-container" style="padding-top: 0; margin-top: 0;">
    <!-- Title Group -->
    <div class="title-group" data-aos="fade-up">
      <h2 class="main-title glitch" data-text="{{ section.settings.main_title | default: 'Key Features' }}">{{ section.settings.main_title | default: 'Key Features' }}</h2>
      <div class="title-underline"></div>
      <p class="subtitle">{{ section.settings.subtitle | default: 'Experience three groundbreaking innovations in a single machine: endless batch production, unlimited Z-axis printing, and support-free overhang technology.' }}</p>
    </div>

    <!-- Feature Tags - Moved above feature content -->
    <div class="feature-tags">
      <button class="feature-tag active" data-feature="batch">
        <span class="tag-text">{{ section.settings.tag_1_text | default: "Batch Production" }}</span>
        <div class="tag-bg"></div>
      </button>
      <button class="feature-tag" data-feature="unlimited">
        <span class="tag-text">{{ section.settings.tag_2_text | default: "Unlimited Z-Axis" }}</span>
        <div class="tag-bg"></div>
      </button>
      <button class="feature-tag" data-feature="support-free">
        <span class="tag-text">{{ section.settings.tag_4_text | default: "Support-Free" }}</span>
        <div class="tag-bg"></div>
      </button>
    </div>

    <!-- Feature Display -->
    <div class="feature-showcase">
      <!-- Left Navigation Button -->
      <button class="feature-nav-button prev-button" aria-label="Previous feature">
        <div class="button-bg"></div>
        <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
          <path d="M15 6L9 12L15 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>

      <!-- Feature Content -->
      <div class="feature-content-wrap">
        <!-- Feature Images -->
        <div class="feature-image-container">
          <div class="feature-images">
            <img class="feature-img active" data-feature="batch" src="https://cdn.shopify.com/s/files/1/0762/6113/0493/files/2_666d80de-e5e9-4876-9359-65c297d39b1d.png?v=1752026723" alt="{{ section.settings.feature_1_title | default: 'Unlimited Batch Production' }}">

            <img class="feature-img" data-feature="unlimited" src="https://cdn.shopify.com/s/files/1/0762/6113/0493/files/3_3a4e5883-448e-4355-ae3c-5ec9d8ef11ca.png?v=1752026722" alt="{{ section.settings.feature_2_title | default: 'Break the Z-Axis Limitation' }}">

            <img class="feature-img" data-feature="support-free" src="https://cdn.shopify.com/s/files/1/0762/6113/0493/files/4_0998892e-0f93-4450-a5e0-22f5e068391d.png?v=1752044795" alt="{{ section.settings.feature_3_title | default: 'Support-Free Overhang Printing' }}">
          </div>
        </div>

        <!-- Feature Text Blocks -->
        <div class="feature-text-container">
          <div class="feature-texts">
            <!-- Feature 1 Text -->
            <div class="feature-text active" data-feature="batch">
              <span class="feature-number">01</span>
              <h3 class="feature-title">{{ section.settings.feature_1_title | default: "Unlimited Batch Production" }}</h3>
              <p class="feature-description">{{ section.settings.feature_1_description | default: "Repeat print the same model multiple times or print multiple models at once. Test and adjust slice settings first, then start mass production." }}</p>
              <ul class="feature-bullets">
                <li>{{ section.settings.feature_1_bullet_1 | default: "Multiple models in one batch" }}</li>
                <li>{{ section.settings.feature_1_bullet_2 | default: "Perfect for mass production" }}</li>
                <li>{{ section.settings.feature_1_bullet_3 | default: "Consistent quality across prints" }}</li>
              </ul>
            </div>

            <!-- Feature 2 Text -->
            <div class="feature-text" data-feature="unlimited">
              <span class="feature-number">02</span>
              <h3 class="feature-title">{{ section.settings.feature_2_title | default: "Break the Z-Axis Limitation" }}</h3>
              <p class="feature-description">{{ section.settings.feature_2_description | default: "Print models without length restrictions. Perfect for architectural models, long tools, and oversized prototypes." }}</p>
              <ul class="feature-bullets">
                <li>{{ section.settings.feature_2_bullet_1 | default: "Unlimited Length printing" }}</li>
                <li>{{ section.settings.feature_2_bullet_2 | default: "Perfect for architectural models" }}</li>
                <li>{{ section.settings.feature_2_bullet_3 | default: "No Z-axis height constraints" }}</li>
              </ul>
            </div>

            <!-- Feature 3 Text -->
            <div class="feature-text" data-feature="support-free">
              <span class="feature-number">03</span>
              <h3 class="feature-title">{{ section.settings.feature_3_title | default: "Support-Free Overhang Printing" }}</h3>
              <p class="feature-description">{{ section.settings.feature_3_description | default: "Back printing angles don't need support structures, saving material and post-processing time." }}</p>
              <ul class="feature-bullets">
                <li>{{ section.settings.feature_3_bullet_1 | default: "No Support Needed for overhangs" }}</li>
                <li>{{ section.settings.feature_3_bullet_2 | default: "Saves material and post-processing time" }}</li>
                <li>{{ section.settings.feature_3_bullet_3 | default: "Superior surface finish quality" }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Navigation Button -->
      <button class="feature-nav-button next-button" aria-label="Next feature">
        <div class="button-bg"></div>
        <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
          <path d="M9 6L15 12L9 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>


  </div>
</section>

<style>
/* Override any margin issues */
.key-features-section {
  margin-top: 0 !important;
}

/* Scroll Continue Indicator */
.scroll-continue-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
}

.scroll-continue-indicator.visible {
  opacity: 1;
}

.scroll-continue-indicator .indicator-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 149, 0, 0.3);
  border-radius: 20px;
  padding: 15px 25px;
  color: #fff;
}

.scroll-continue-indicator .indicator-icon {
  font-size: 24px;
  margin-bottom: 8px;
  animation: bounce 2s infinite;
  color: #ff9500;
}

.scroll-continue-indicator .indicator-text {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Mobile adjustments */
@media screen and (max-width: 768px) {
  .scroll-continue-indicator {
    bottom: 20px;
  }

  .scroll-continue-indicator .indicator-content {
    padding: 12px 20px;
  }

  .scroll-continue-indicator .indicator-icon {
    font-size: 20px;
  }

  .scroll-continue-indicator .indicator-text {
    font-size: 12px;
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const keyFeaturesSection = document.getElementById('key-features-{{ section.id }}');
  if (!keyFeaturesSection) return;

  // Initialize GSAP if available
  const hasGsap = typeof gsap !== 'undefined';
  const hasScrollTrigger = hasGsap && typeof ScrollTrigger !== 'undefined';

  if (hasGsap && hasScrollTrigger) {
    gsap.registerPlugin(ScrollTrigger);
  }

  // Optimized scroll lock system - smooth and stable
  let isScrollLocked = false;
  let scrollLockPosition = 0;
  let isInSection = false;
  // 🔧 添加导航模式标志，防止导航期间重新锁定
  let isNavigationMode = false;
  let navigationModeTimer = null;

  // 🔧 移动端检测：完全禁用移动端滚动锁定功能
  const isMobileDevice = () => {
    return window.innerWidth <= 768 ||
           /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
           ('ontouchstart' in window) ||
           (navigator.maxTouchPoints > 0);
  };

  // Elements
  const prevButton = keyFeaturesSection.querySelector('.prev-button');
  const nextButton = keyFeaturesSection.querySelector('.next-button');
  const featureTags = keyFeaturesSection.querySelectorAll('.feature-tag');
  const featureImages = keyFeaturesSection.querySelectorAll('.feature-img');
  const featureTexts = keyFeaturesSection.querySelectorAll('.feature-text');

  // Feature data
  const features = ['batch', 'unlimited', 'support-free'];
  let currentIndex = 0;
  let isInitialized = false;

  // Optimized scroll lock system - smooth and stable
  let hasViewedAllFeatures = false;
  let viewedFeatures = new Set(); // Start empty - user must actively view each feature
  let lastScrollTime = 0;
  let scrollCooldown = false;
  let isAnimating = false; // Track if feature animation is in progress
  let currentAnimationTimeline = null; // Store current animation timeline
  let lastScrollY = 0;
  let scrollTimer = null;
  let isScrollingFast = false;
  let scrollEndTimer = null;

  // Enhanced scroll velocity tracking for continuous scroll detection
  let scrollVelocityHistory = [];
  let scrollStabilityTimer = null;
  let isScrollStable = true;
  let consecutiveScrollCount = 0;
  let lastScrollDirection = 0;
  let scrollAcceleration = 0;

  // Optimized thresholds for continuous scrolling
  const VELOCITY_HISTORY_SIZE = 5; // Increased for better pattern detection
  const FAST_SCROLL_THRESHOLD = 60; // Increased to be less sensitive
  const CONTINUOUS_SCROLL_THRESHOLD = 25; // New threshold for continuous scrolling
  const STABILITY_DELAY = 200; // Slightly increased for stability
  const MAX_CONTINUOUS_SCROLLS = 8; // Allow up to 8 continuous scrolls before forcing check

  // Simplified and reliable scroll lock implementation
  function enableScrollLock() {
    // 🔧 移动端完全跳过滚动锁定
    if (isMobileDevice()) {
      console.log('Mobile device detected - skipping scroll lock');
      return;
    }

    if (isScrollLocked) return;

    console.log('Enabling scroll lock');
    isScrollLocked = true;

    // 🔧 修复自动轮播冲突：滚动锁定时强制停止自动轮播
    console.log('🔒 Scroll lock enabled - forcing auto-rotation stop');
    console.log('🔒 Before stop - autoRotateInterval:', autoRotateInterval);
    stopAutoRotate();

    // 🔧 双重保险：直接清理定时器
    if (autoRotateInterval) {
      console.log('🔒 Double-safety: clearing autoRotateInterval directly');
      clearInterval(autoRotateInterval);
      autoRotateInterval = null;
    }

    console.log('🔒 After stop - autoRotateInterval:', autoRotateInterval);

    // 🔧 标记当前显示的feature为已查看（解决轮播导致的额外滚动问题）
    viewedFeatures.add(currentIndex);
    console.log('Current feature', currentIndex, 'marked as viewed during scroll lock');

    // Store current scroll position
    scrollLockPosition = window.scrollY;
    const rect = keyFeaturesSection.getBoundingClientRect();

    // Ensure component is at the top
    if (Math.abs(rect.top) > 20) {
      console.log('Adjusting position for optimal lock');
      scrollLockPosition = window.scrollY + rect.top;
      window.scrollTo(0, scrollLockPosition);
    }

    // Apply scroll lock
    document.body.style.position = 'fixed';
    document.body.style.top = `-${scrollLockPosition}px`;
    document.body.style.left = '0';
    document.body.style.right = '0';
    document.body.style.width = '100%';
    document.body.style.overflow = 'hidden';

    // Add scroll event listeners
    document.addEventListener('wheel', handleScrollInSection, { passive: false });
    document.addEventListener('touchmove', handleTouchInSection, { passive: false });
    document.addEventListener('keydown', handleKeyInSection, { passive: false });

    // Add visual indicator
    document.body.classList.add('scroll-locked');

    console.log('Scroll locked at position:', scrollLockPosition);
  }

  function disableScrollLock() {
    if (!isScrollLocked) return;

    console.log('Disabling scroll lock smoothly');
    isScrollLocked = false;

    // Apply smooth transition for unlock
    document.body.style.transition = 'transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';

    // Restore body styles
    document.body.style.position = '';
    document.body.style.top = '';
    document.body.style.left = '';
    document.body.style.right = '';
    document.body.style.width = '';
    document.body.style.overflow = '';

    // Remove visual indicator
    document.body.classList.remove('scroll-locked');

    // Restore scroll position smoothly
    window.scrollTo({
      top: scrollLockPosition,
      behavior: 'auto' // Use auto to prevent additional smoothing
    });

    // Remove event listeners
    document.removeEventListener('wheel', handleScrollInSection, { passive: false });
    document.removeEventListener('touchmove', handleTouchInSection, { passive: false });
    document.removeEventListener('keydown', handleKeyInSection, { passive: false });

    // Clear timers
    clearTimeout(scrollTimer);
    clearTimeout(scrollEndTimer);

    // Remove transition after unlock
    setTimeout(() => {
      document.body.style.transition = '';
    }, 300);

    // Reset section state
    isInSection = false;

    // 🔧 修复自动轮播冲突：解锁滚动后恢复自动轮播（只有在未查看完所有功能时才恢复）
    if (!hasViewedAllFeatures) {
      startAutoRotate();
      console.log('Auto-rotation resumed after scroll unlock - not all features viewed yet');
    } else {
      console.log('Auto-rotation not resumed - all features already viewed');
    }

    console.log('Scroll unlocked smoothly');
  }

  // Enhanced debug function with continuous scroll information
  function debugScrollLockStatus() {
    const rect = keyFeaturesSection.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const visibleHeight = Math.min(rect.bottom, viewportHeight) - Math.max(rect.top, 0);
    const visibleRatio = Math.max(0, visibleHeight) / rect.height;
    const avgVelocity = scrollVelocityHistory.length > 0
      ? scrollVelocityHistory.reduce((sum, v) => sum + v, 0) / scrollVelocityHistory.length
      : 0;

    console.log('🔍 Enhanced Scroll Lock Debug Status:', {
      // Basic status
      isScrollLocked,
      hasViewedAllFeatures,
      isScrollingFast,
      isScrollStable,
      isInSection,
      currentIndex,
      viewedFeatures: Array.from(viewedFeatures),

      // Position info
      rectTop: rect.top.toFixed(0),
      visibleRatio: visibleRatio.toFixed(2),
      scrollLockPosition,
      windowScrollY: window.scrollY.toFixed(0),

      // Continuous scroll info
      consecutiveScrollCount,
      lastScrollDirection,
      avgVelocity: avgVelocity.toFixed(3),
      velocityHistory: scrollVelocityHistory.map(v => v.toFixed(3)),

      // Thresholds
      FAST_SCROLL_THRESHOLD,
      CONTINUOUS_SCROLL_THRESHOLD,
      MAX_CONTINUOUS_SCROLLS,
      STABILITY_DELAY
    });
  }

  // Make debug function available globally for console testing
  window.debugIR3ScrollLock = debugScrollLockStatus;

  // Enhanced scroll detection with continuous scroll support
  function initializeScrollDetection() {
    // 🔧 移动端完全跳过滚动事件监听器注册，避免干扰自然滚动
    if (isMobileDevice()) {
      console.log('Mobile device detected - skipping scroll event listener registration');
      return;
    }

    // Global scroll handler with continuous scroll logic (仅桌面端)
    window.addEventListener('scroll', function() {
      const currentScrollY = window.scrollY;
      const currentTime = performance.now();
      const scrollDelta = currentScrollY - lastScrollY;
      const timeDelta = currentTime - (lastScrollTime || currentTime);

      // Calculate velocity (pixels per millisecond)
      const velocity = timeDelta > 0 ? Math.abs(scrollDelta) / timeDelta : 0;

      // Track scroll direction changes
      const currentDirection = scrollDelta > 0 ? 1 : scrollDelta < 0 ? -1 : 0;
      const directionChanged = currentDirection !== 0 && currentDirection !== lastScrollDirection;

      if (directionChanged) {
        consecutiveScrollCount = 0; // Reset on direction change
      } else if (Math.abs(scrollDelta) > 5) {
        consecutiveScrollCount++;
      }

      // Update velocity history
      scrollVelocityHistory.push(velocity);
      if (scrollVelocityHistory.length > VELOCITY_HISTORY_SIZE) {
        scrollVelocityHistory.shift();
      }

      // Calculate average velocity for smoother detection
      const avgVelocity = scrollVelocityHistory.length > 0
        ? scrollVelocityHistory.reduce((sum, v) => sum + v, 0) / scrollVelocityHistory.length
        : 0;

      // Enhanced fast scrolling detection
      const isSingleFastScroll = Math.abs(scrollDelta) > FAST_SCROLL_THRESHOLD;
      const isContinuousFastScroll = avgVelocity > 0.8 && consecutiveScrollCount > 3;
      const isContinuousModerateScroll = Math.abs(scrollDelta) > CONTINUOUS_SCROLL_THRESHOLD && consecutiveScrollCount > 2;

      // Determine if we should consider this "fast scrolling"
      isScrollingFast = isSingleFastScroll || isContinuousFastScroll;

      // Force check after many continuous scrolls (prevent infinite delay)
      const shouldForceCheck = consecutiveScrollCount >= MAX_CONTINUOUS_SCROLLS;

      lastScrollY = currentScrollY;
      lastScrollTime = currentTime;
      lastScrollDirection = currentDirection;
      isScrollStable = false;

      // Clear existing timer
      clearTimeout(scrollEndTimer);

      console.log('📊 Scroll analysis:', {
        scrollDelta: scrollDelta.toFixed(0),
        velocity: velocity.toFixed(3),
        avgVelocity: avgVelocity.toFixed(3),
        consecutiveCount: consecutiveScrollCount,
        isSingleFast: isSingleFastScroll,
        isContinuousFast: isContinuousFastScroll,
        isContinuousModerate: isContinuousModerateScroll,
        isScrollingFast,
        shouldForceCheck
      });

      // Always check visibility immediately for responsiveness
      checkSectionVisibility();

      // For continuous moderate scrolling or forced check, use shorter delay
      const dynamicDelay = (isContinuousModerateScroll || shouldForceCheck) ? 100 : STABILITY_DELAY;

      // Wait for scrolling to stabilize, then check again
      scrollEndTimer = setTimeout(() => {
        isScrollingFast = false;
        isScrollStable = true;
        consecutiveScrollCount = Math.max(0, consecutiveScrollCount - 1); // Gradually reduce count
        checkSectionVisibility();
      }, dynamicDelay);

    }, { passive: true });
  }

  // Enhanced section visibility check with continuous scroll support
  function checkSectionVisibility() {
    // 🔧 移动端跳过滚动锁定检查
    if (isMobileDevice()) {
      console.log('Mobile device - skipping scroll lock visibility check');
      return;
    }

    if (hasViewedAllFeatures) {
      console.log('All features viewed, skipping visibility check');
      return;
    }

    const rect = keyFeaturesSection.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const currentScrollY = window.scrollY;

    // Calculate visibility ratio
    const visibleHeight = Math.min(rect.bottom, viewportHeight) - Math.max(rect.top, 0);
    const visibleRatio = Math.max(0, visibleHeight) / rect.height;

    // Adaptive conditions based on scroll behavior
    const isApproachingSection = rect.top <= viewportHeight * 0.4; // Increased to 40% for continuous scroll
    const isSectionVisible = visibleRatio > 0.4; // Reduced to 40% for easier triggering
    const isNearTop = rect.top <= 200 && rect.top >= -200; // Increased range for continuous scroll

    // Special handling for continuous scrolling
    const isContinuousScrolling = consecutiveScrollCount > 2;
    const shouldAllowContinuousLock = isContinuousScrolling && isApproachingSection && visibleRatio > 0.3;

    console.log('🔍 Enhanced visibility check:', {
      rectTop: rect.top.toFixed(0),
      rectBottom: rect.bottom.toFixed(0),
      visibleRatio: visibleRatio.toFixed(2),
      viewportHeight,
      isApproachingSection,
      isSectionVisible,
      isNearTop,
      isScrollLocked,
      isScrollingFast,
      isContinuousScrolling,
      shouldAllowContinuousLock,
      consecutiveScrollCount,
      currentScrollY: currentScrollY.toFixed(0)
    });

    // Enhanced conditions for continuous scrolling
    const basicConditionsMet = isApproachingSection && isSectionVisible && isNearTop && !isScrollLocked;
    const continuousConditionsMet = shouldAllowContinuousLock && !isScrollLocked;

    if (basicConditionsMet || continuousConditionsMet) {
      // 🔧 检查导航模式，防止导航期间重新锁定
      if (isNavigationMode) {
        console.log('🚀 Navigation mode active - skipping scroll lock attempt');
        return;
      }

      isInSection = true;
      const lockType = basicConditionsMet ? 'basic' : 'continuous';
      console.log(`✅ ${lockType} conditions met, attempting scroll lock`);

      clearTimeout(scrollTimer);

      // Dynamic delay based on scroll type
      let lockDelay = 50; // Default fast response
      if (isScrollingFast && !isContinuousScrolling) {
        lockDelay = 150; // Longer delay for single fast scroll
      } else if (isContinuousScrolling) {
        lockDelay = 80; // Medium delay for continuous scroll
      }

      scrollTimer = setTimeout(() => {
        if (isInSection && !hasViewedAllFeatures && !isScrollLocked) {
          console.log(`🔒 Executing ${lockType} scroll lock attempt`);
          attemptRobustScrollLock();
        }
      }, lockDelay);
    } else {
      if (isInSection) {
        console.log('❌ Conditions no longer met, resetting isInSection');
        isInSection = false;
      }

      // Check if we should unlock
      if ((!isSectionVisible || !isNearTop) && isScrollLocked && hasViewedAllFeatures) {
        console.log('🔓 Section out of range and all features viewed, unlocking');
        disableScrollLock();
      }
    }
  }

  // Simplified scroll lock attempt with better logging
  function attemptRobustScrollLock() {
    // 🔧 移动端完全跳过滚动锁定尝试
    if (isMobileDevice()) {
      console.log('⚠️ Mobile device - scroll lock attempt skipped');
      return;
    }

    if (isScrollLocked || hasViewedAllFeatures) {
      console.log('⚠️ Lock attempt aborted:', { isScrollLocked, hasViewedAllFeatures });
      return;
    }

    console.log('🔒 Attempting scroll lock...');

    const rect = keyFeaturesSection.getBoundingClientRect();
    const currentScrollY = window.scrollY;

    // Calculate ideal position (component at top of viewport)
    const idealScrollY = currentScrollY + rect.top;
    const positionDiff = Math.abs(rect.top);

    console.log('📍 Lock positioning analysis:', {
      currentScrollY: currentScrollY.toFixed(0),
      rectTop: rect.top.toFixed(0),
      idealScrollY: idealScrollY.toFixed(0),
      positionDiff: positionDiff.toFixed(0),
      isScrollingFast
    });

    if (positionDiff > 30) {
      // Position needs adjustment
      console.log('📐 Adjusting position before lock');
      window.scrollTo({
        top: idealScrollY,
        behavior: 'auto'
      });

      // Lock after position adjustment
      setTimeout(() => {
        console.log('🔒 Enabling lock after position adjustment');
        enableScrollLock();
      }, 30);
    } else {
      // Position is good, lock immediately
      console.log('✅ Position is good, locking immediately');
      enableScrollLock();
    }
  }

  // Initialize feature states properly
  function initializeFeatures() {
    if (isInitialized) return;

    console.log('Initializing IR3 Key Features...');

    // Set initial states for all images with explicit CSS
    featureImages.forEach((img, i) => {
      img.classList.remove('active', 'prev', 'next');

      if (i === 0) {
        // First image: active state
        img.classList.add('active');
        img.style.cssText = `
          opacity: 1 !important;
          visibility: visible !important;
          transform: scale(1.3) translateX(0) translateZ(0) rotateY(0) !important;
          filter: blur(0) brightness(1) !important;
          z-index: 3 !important;
          transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
        `;
      } else if (i === 1) {
        // Second image: next state
        img.classList.add('next');
        img.style.cssText = `
          opacity: 0.15 !important;
          visibility: visible !important;
          transform: scale(0.7) translateX(30%) translateZ(-100px) rotateY(15deg) !important;
          filter: blur(3px) brightness(0.6) !important;
          z-index: 1 !important;
          transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
        `;
      } else if (i === 2) {
        // Third image: prev state
        img.classList.add('prev');
        img.style.cssText = `
          opacity: 0.15 !important;
          visibility: visible !important;
          transform: scale(0.7) translateX(-30%) translateZ(-100px) rotateY(-15deg) !important;
          filter: blur(3px) brightness(0.6) !important;
          z-index: 1 !important;
          transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
        `;
      }
    });

    // Set initial states for text blocks
    featureTexts.forEach((text, i) => {
      text.classList.toggle('active', i === 0);
    });

    // Set initial states for tags
    featureTags.forEach((tag, i) => {
      tag.classList.toggle('active', i === 0);
      // Ensure tags are visible
      tag.style.opacity = '1';
      tag.style.transform = 'none';
      tag.style.visibility = 'visible';
    });

    // Mark the first feature as viewed when component initializes
    viewedFeatures.add(0);
    console.log('First feature (index 0) marked as viewed during initialization');
    console.log('Viewed features:', Array.from(viewedFeatures));

    isInitialized = true;
    console.log('IR3 Key Features initialized successfully');
  }

  // Handle scroll events while section is locked
  function handleScrollInSection(e) {
    // 🔧 移动端直接允许自然滚动
    if (isMobileDevice()) {
      console.log('Mobile device - allowing natural scroll');
      return;
    }

    if (hasViewedAllFeatures) {
      console.log('All features viewed, allowing natural scroll');
      disableScrollLock();
      return;
    }

    if (!isScrollLocked || scrollCooldown || isAnimating) {
      if (isAnimating) {
        console.log('Animation in progress, blocking scroll');
      }
      return;
    }

    e.preventDefault();
    e.stopPropagation();

    // Improved throttle
    const now = Date.now();
    if (now - lastScrollTime < 400) return;

    const delta = e.deltaY;
    const absDelta = Math.abs(delta);

    // Ignore very small movements
    if (absDelta < 10) return;

    lastScrollTime = now;
    scrollCooldown = true;

    console.log(`Scroll in section: delta=${delta}, currentIndex=${currentIndex}`);

    if (delta > 0) {
      // Scrolling down - next feature
      if (currentIndex < features.length - 1) {
        changeFeature(currentIndex + 1);
      } else {
        // At last feature, check if all viewed
        checkAllFeaturesViewed();
      }
    } else {
      // Scrolling up - previous feature
      if (currentIndex > 0) {
        changeFeature(currentIndex - 1);
      } else {
        // At first feature - allow exit if all features viewed
        if (viewedFeatures.size >= features.length) {
          console.log('All features viewed, allowing scroll up to exit');
          disableScrollLock();
        }
      }
    }

    // Reset cooldown
    setTimeout(() => {
      scrollCooldown = false;
    }, 1600);
  }

  function handleTouchInSection(e) {
    // 🔧 移动端允许正常触摸滚动
    if (isMobileDevice()) {
      return;
    }

    if (!isInSection || !isScrollLocked) return;
    e.preventDefault();
  }

  function handleKeyInSection(e) {
    // 🔧 移动端允许正常键盘导航
    if (isMobileDevice()) {
      return;
    }

    if (!isInSection || !isScrollLocked) return;

    if (e.key === 'ArrowDown' || e.key === 'PageDown') {
      e.preventDefault();
      if (currentIndex < features.length - 1) {
        changeFeature(currentIndex + 1);
      } else if (hasViewedAllFeatures) {
        disableScrollLock();
      }
    } else if (e.key === 'ArrowUp' || e.key === 'PageUp') {
      e.preventDefault();
      if (currentIndex > 0) {
        changeFeature(currentIndex - 1);
      } else if (hasViewedAllFeatures) {
        disableScrollLock();
      }
    } else if (e.key === 'Escape') {
      disableScrollLock();
    }
  }

  // Check if all features have been viewed
  function checkAllFeaturesViewed() {
    if (viewedFeatures.size >= features.length && !hasViewedAllFeatures) {
      hasViewedAllFeatures = true;
      console.log('All features viewed! Unlocking scroll after delay.');

      // 🔧 修复自动轮播冲突：所有功能查看完毕后停止自动轮播
      stopAutoRotate();
      console.log('Auto-rotation stopped - all features viewed');

      // Show completion indicator
      showCompletionIndicator();

      // Unlock scroll after delay
      setTimeout(() => {
        if (isScrollLocked) {
          disableScrollLock();
        }
      }, 2000);
    }
  }



  function showCompletionIndicator() {
    // Create a subtle indicator that user can now scroll down
    const indicator = document.createElement('div');
    indicator.className = 'scroll-continue-indicator';
    indicator.innerHTML = `
      <div class="indicator-content">
        <div class="indicator-icon">↓</div>
        <div class="indicator-text">Scroll down to continue</div>
      </div>
    `;

    keyFeaturesSection.appendChild(indicator);

    // Animate in
    setTimeout(() => {
      indicator.classList.add('visible');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
      indicator.classList.remove('visible');
      setTimeout(() => {
        if (indicator.parentNode) {
          indicator.remove();
        }
      }, 500);
    }, 3000);
  }

  // Debug logging for production
  console.log('=== IR3 Key Features Debug Info ===');
  console.log('GSAP available:', hasGsap);
  console.log('ScrollTrigger available:', hasScrollTrigger);
  console.log('keyFeaturesSection found:', !!keyFeaturesSection);
  console.log('featureImages count:', featureImages.length);
  console.log('featureTexts count:', featureTexts.length);
  console.log('featureTags count:', featureTags.length);

  // Disable AOS for specific elements to prevent conflicts
  const imageContainer = keyFeaturesSection.querySelector('.feature-image-container');
  const textContainer = keyFeaturesSection.querySelector('.feature-text-container');

  if (imageContainer) {
    imageContainer.removeAttribute('data-aos');
    imageContainer.classList.remove('aos-init', 'aos-animate');
    imageContainer.style.opacity = '1';
    imageContainer.style.transform = 'none';
  }

  if (textContainer) {
    textContainer.removeAttribute('data-aos');
    textContainer.classList.remove('aos-init', 'aos-animate');
    textContainer.style.opacity = '1';
    textContainer.style.transform = 'none';
  }
  
  // Initialize scroll animations
  if (hasGsap && hasScrollTrigger) {
    // Title animation
    gsap.from(keyFeaturesSection.querySelector('.title-group'), {
      scrollTrigger: {
        trigger: keyFeaturesSection,
        start: 'top 80%'
      },
      opacity: 0,
      y: 30,
      duration: 0.8
    });
    
    // Feature content animation - synchronized
    const featureContentTl = gsap.timeline({
      scrollTrigger: {
        trigger: keyFeaturesSection.querySelector('.feature-showcase'),
        start: 'top 75%'
      }
    });

    featureContentTl
      .from(keyFeaturesSection.querySelector('.feature-image-container'), {
        opacity: 0,
        x: -50,
        duration: 1.0,
        ease: "power2.out"
      })
      .from(keyFeaturesSection.querySelector('.feature-text-container'), {
        opacity: 0,
        x: 50,
        duration: 1.0,
        ease: "power2.out"
      }, "<"); // Start at exactly the same time as images
    
    // Tags animation - improved reliability
    const featureTags = keyFeaturesSection.querySelectorAll('.feature-tag');

    // Ensure tags are visible by default
    featureTags.forEach(tag => {
      tag.style.opacity = '1';
      tag.style.transform = 'none';
    });

    // Then animate them
    gsap.from(featureTags, {
      scrollTrigger: {
        trigger: keyFeaturesSection,
        start: 'top 85%',
        once: true
      },
      opacity: 0,
      y: 20,
      stagger: 0.1,
      duration: 0.6,
      onComplete: () => {
        // Ensure tags are visible after animation
        featureTags.forEach(tag => {
          tag.style.opacity = '1';
          tag.style.transform = 'none';
        });
      }
    });
  }
  
  // Change feature function
  function changeFeature(index) {
    console.log('changeFeature called with index:', index);
    console.log('Current index before change:', currentIndex);

    // 🔧 移动端优化：允许更快的功能切换，减少动画阻塞
    if (isAnimating && !isMobileDevice()) {
      console.log('Animation in progress, ignoring new changeFeature request (desktop only)');
      return;
    }

    // 🔧 移动端：如果有动画在进行，先停止当前动画
    if (isAnimating && isMobileDevice() && currentAnimationTimeline) {
      console.log('Mobile: Stopping current animation to allow new feature change');
      currentAnimationTimeline.kill();
      isAnimating = false;
    }

    // Ensure index is within range
    if (index < 0) index = features.length - 1;
    if (index >= features.length) index = 0;

    const prevIndex = currentIndex;

    // Mark this feature as viewed (user interaction)
    viewedFeatures.add(index);
    console.log('Feature', index, 'marked as viewed');
    console.log('Viewed features:', Array.from(viewedFeatures));
    console.log('Total viewed:', viewedFeatures.size, '/ Required:', features.length);

    // Set animation state
    isAnimating = true;
    console.log('Animation started for feature', index);
    currentIndex = index;
    const currentFeature = features[currentIndex];

    // Immediately update tags state for instant feedback
    featureTags.forEach(tag => {
      const tagFeature = tag.getAttribute('data-feature');
      tag.classList.toggle('active', tagFeature === currentFeature);
    });

    // Calculate previous and next indices for 3D effect
    const nextIndex = (currentIndex + 1) % features.length;
    const previousIndex = (currentIndex - 1 + features.length) % features.length;
    
    // Use GSAP for smooth transitions if available
    if (hasGsap) {
      // Animate out current
      const tl = gsap.timeline({
        onComplete: () => {
          // Animation completed, allow next scroll
          isAnimating = false;
          console.log('Animation completed for feature', currentIndex);
        }
      });

      // Store current timeline reference
      currentAnimationTimeline = tl;
      
      // Animate navigation buttons
      tl.to([prevButton, nextButton], {
        scale: 0.95,
        duration: 0.2,
        ease: "back.out(1.5)",
        onComplete: () => {
          gsap.to([prevButton, nextButton], {
            scale: 1,
            duration: 0.4,
            ease: "elastic.out(1, 0.3)"
          });
        }
      });
      
      // Update all classes first to set initial state and clear previous state
      featureImages.forEach((img, i) => {
        img.classList.remove('active', 'prev', 'next');
        // Hide any non-relevant images completely
        if (i !== currentIndex && i !== previousIndex && i !== nextIndex) {
          img.style.visibility = 'hidden';
          img.style.opacity = '0';
        } else {
          img.style.visibility = 'visible';
        }

        // Set appropriate classes
        if (i === currentIndex) {
          img.classList.add('active');
        } else if (i === previousIndex) {
          img.classList.add('prev');
        } else if (i === nextIndex) {
          img.classList.add('next');
        }
      });

      // Immediately hide all text blocks except current one - STRICT control
      featureTexts.forEach((text, i) => {
        if (i !== currentIndex) {
          text.style.visibility = 'hidden';
          text.style.opacity = '0';
          text.style.display = 'none';  // 👈 强制隐藏
          text.classList.remove('active');
        } else {
          text.style.visibility = 'visible';
          text.style.display = 'block';  // 👈 强制显示
          text.classList.add('active');
        }
      });
      
      // Animate out current content with more sophisticated transitions
      tl.to(featureImages, {
        opacity: index => (index === currentIndex ? 1 : (index === previousIndex || index === nextIndex ? 0.15 : 0)),
        scale: index => {
          if (index === currentIndex) return 1.3;
          if (index === previousIndex || index === nextIndex) return 0.7;
          return 0;
        },
        x: index => {
          if (index === currentIndex) return 0;
          if (index === previousIndex) return '-30%';
          if (index === nextIndex) return '30%';
          return 0;
        },
        z: index => {
          if (index === currentIndex) return 0;
          if (index === previousIndex || index === nextIndex) return -100;
          return 0;
        },
        rotateY: index => {
          if (index === currentIndex) return 0;
          if (index === previousIndex) return '-15deg';
          if (index === nextIndex) return '15deg';
          return 0;
        },
        filter: index => (index === currentIndex ? 'blur(0) brightness(1)' : 'blur(3px) brightness(0.6)'),
        duration: 1.0,
        ease: "power2.out",
        stagger: 0.0
      });
      
      // Simple and fast text transition - synchronized with images
      tl.fromTo(featureTexts[currentIndex],
        {
          opacity: 0,
          x: -10,
          scale: 0.98
        },
        {
          opacity: 1,
          x: 0,
          scale: 1,
          duration: 1.0, // Match image animation duration
          ease: "power2.out"
        },
        "<" // Start at same time as images
      );

      // Create a high-tech flash effect
      const flashOverlay = document.createElement('div');
      flashOverlay.style.position = 'absolute';
      flashOverlay.style.top = '0';
      flashOverlay.style.left = '0';
      flashOverlay.style.width = '100%';
      flashOverlay.style.height = '100%';
      flashOverlay.style.background = 'linear-gradient(135deg, rgba(255, 149, 0, 0.05) 0%, rgba(255, 95, 109, 0.05) 50%, transparent 100%)';
      flashOverlay.style.pointerEvents = 'none';
      flashOverlay.style.zIndex = '10';
      flashOverlay.style.opacity = '0';
      flashOverlay.style.backdropFilter = 'blur(4px)';
      keyFeaturesSection.querySelector('.feature-showcase').appendChild(flashOverlay);

      tl.to(flashOverlay, {
        opacity: 0.5,
        duration: 0.15,
        ease: "power2.in",
        onComplete: () => {
          gsap.to(flashOverlay, {
            opacity: 0,
            duration: 0.5,
            ease: "power2.out",
            onComplete: () => flashOverlay.remove()
          });
        }
      }, "<0.1"); // Start flash slightly after images
      
      // Add particle effect on transition
      if (typeof gsap.utils !== 'undefined') {
        const particleCount = 20;
        const particleContainer = document.createElement('div');
        particleContainer.style.position = 'absolute';
        particleContainer.style.top = '0';
        particleContainer.style.left = '0';
        particleContainer.style.width = '100%';
        particleContainer.style.height = '100%';
        particleContainer.style.pointerEvents = 'none';
        particleContainer.style.zIndex = '5';
        keyFeaturesSection.querySelector('.feature-content-wrap').appendChild(particleContainer);
        
        for (let i = 0; i < particleCount; i++) {
          const particle = document.createElement('div');
          particle.style.position = 'absolute';
          particle.style.width = gsap.utils.random(1, 3) + 'px';
          particle.style.height = gsap.utils.random(1, 3) + 'px';
          particle.style.backgroundColor = Math.random() > 0.5 ? 'rgba(255, 149, 0, 0.8)' : 'rgba(255, 95, 109, 0.8)';
          particle.style.borderRadius = '50%';
          particle.style.top = gsap.utils.random(30, 70) + '%';
          particle.style.left = gsap.utils.random(40, 60) + '%';
          particle.style.opacity = 0;
          particle.style.boxShadow = '0 0 4px ' + (Math.random() > 0.5 ? 'rgba(255, 149, 0, 0.8)' : 'rgba(255, 95, 109, 0.8)');
          particleContainer.appendChild(particle);
          
          gsap.to(particle, {
            opacity: gsap.utils.random(0.4, 0.8),
            x: gsap.utils.random(-100, 100),
            y: gsap.utils.random(-100, 100),
            duration: gsap.utils.random(0.8, 1.5),
            delay: gsap.utils.random(0, 0.2),
            ease: "power1.out",
            onComplete: () => {
              gsap.to(particle, {
                opacity: 0,
                duration: 0.3,
                onComplete: () => {
                  particle.remove();
                  if (i === particleCount - 1) {
                    particleContainer.remove();
                  }
                }
              });
            }
          });
        }
      }
    } else {
      // Fallback without GSAP
      console.log('Using fallback animation (no GSAP)');

      // Update images
      featureImages.forEach((img, i) => {
        img.classList.toggle('active', i === currentIndex);
        console.log('Set image', i, 'active state:', i === currentIndex);
      });

      // Update text blocks
      featureTexts.forEach((text, i) => {
        text.classList.toggle('active', i === currentIndex);
        console.log('Set text', i, 'active state:', i === currentIndex);
      });
    }

    // Note: checkAllFeaturesViewed is now called from button/tag click handlers only
    // The 2-second delay logic is handled there
  }
  
  // Set up button click handlers
  console.log('Setting up button click handlers...');
  console.log('prevButton:', prevButton);
  console.log('nextButton:', nextButton);

  if (prevButton) {
    prevButton.addEventListener('click', () => {
      console.log('Prev button clicked, current index:', currentIndex);
      // 🔧 修复自动轮播冲突：用户手动交互时停止自动轮播
      stopAutoRotate();
      const prevIndex = (currentIndex - 1 + features.length) % features.length;
      console.log('Calculated prev index:', prevIndex);
      changeFeature(prevIndex);

      // Check if all features viewed after button click
      checkAllFeaturesViewed();
    });
    console.log('Prev button event listener added');
  } else {
    console.error('Prev button not found!');
  }

  if (nextButton) {
    nextButton.addEventListener('click', () => {
      console.log('Next button clicked, current index:', currentIndex);
      // 🔧 修复自动轮播冲突：用户手动交互时停止自动轮播
      stopAutoRotate();
      const nextIndex = (currentIndex + 1) % features.length;
      console.log('Calculated next index:', nextIndex);
      changeFeature(nextIndex);

      // Check if all features viewed after button click
      checkAllFeaturesViewed();
    });
    console.log('Next button event listener added');
  } else {
    console.error('Next button not found!');
  }

  // Set up tag clicks
  featureTags.forEach(tag => {
    tag.addEventListener('click', () => {
      // 🔧 修复自动轮播冲突：用户手动交互时停止自动轮播
      stopAutoRotate();
      const feature = tag.getAttribute('data-feature');
      const index = features.indexOf(feature);
      if (index >= 0) {
        changeFeature(index);
        // Check if all features viewed after tag click with 2-second delay
        checkAllFeaturesViewed();
      }
    });
  });
  
  // Touch swipe functionality for mobile
  const featureShowcase = keyFeaturesSection.querySelector('.feature-showcase');
  let touchStartX = 0;
  let touchEndX = 0;
  let touchStartY = 0;
  let touchEndY = 0;
  let isTouchSwiping = false;

  // 🔧 移动端完全禁用触摸滑动功能，避免干扰页面滚动
  if (!isMobileDevice()) {
    // 仅在桌面端启用触摸滑动功能
    featureShowcase.addEventListener('touchstart', e => {
      // 🔧 优化：记录触摸起始位置（包括Y轴）
      touchStartX = e.changedTouches[0].screenX;
      touchStartY = e.changedTouches[0].screenY;
      isTouchSwiping = false;

      // 🔧 延迟停止自动轮播，避免与滑动手势冲突
      setTimeout(() => {
        if (!isTouchSwiping) {
          stopAutoRotate();
        }
      }, 100);
    }, { passive: true });

    featureShowcase.addEventListener('touchmove', e => {
      // 🔧 检测是否为水平滑动手势
      const currentX = e.changedTouches[0].screenX;
      const currentY = e.changedTouches[0].screenY;
      const deltaX = Math.abs(currentX - touchStartX);
      const deltaY = Math.abs(currentY - touchStartY);

      // 如果水平滑动距离大于垂直滑动距离，认为是滑动手势
      if (deltaX > deltaY && deltaX > 10) {
        isTouchSwiping = true;
      }
    }, { passive: true });

    featureShowcase.addEventListener('touchend', e => {
      touchEndX = e.changedTouches[0].screenX;
      touchEndY = e.changedTouches[0].screenY;

      // 🔧 只有在确认是滑动手势时才处理
      if (isTouchSwiping) {
        handleSwipe();
      }

      // 重置状态
      isTouchSwiping = false;
    }, { passive: true });
  } else {
    console.log('Mobile device detected - touch swipe functionality disabled to allow natural scrolling');
  }

  function handleSwipe() {
    // 🔧 移动端完全禁用滑动处理，避免干扰页面滚动
    if (isMobileDevice()) {
      console.log('Mobile device - swipe handling disabled');
      return;
    }

    const swipeThreshold = 50;
    const horizontalDistance = Math.abs(touchEndX - touchStartX);
    const verticalDistance = Math.abs(touchEndY - touchStartY);

    // 🔧 确保是水平滑动而非垂直滚动
    if (horizontalDistance < swipeThreshold || verticalDistance > horizontalDistance) {
      return;
    }

    if (touchStartX - touchEndX > swipeThreshold) {
      // Swipe left, go to next feature
      stopAutoRotate();
      changeFeature(currentIndex + 1);
      checkAllFeaturesViewed();
    } else if (touchEndX - touchStartX > swipeThreshold) {
      // Swipe right, go to previous feature
      stopAutoRotate();
      changeFeature(currentIndex - 1);
      checkAllFeaturesViewed();
    }
  }
  
  // Auto-rotation (optional)
  let autoRotateInterval = null;
  
  function startAutoRotate() {
    console.log('🚀 startAutoRotate() called');
    console.log('🚀 Current state:', { isScrollLocked, hasViewedAllFeatures, autoRotateInterval });

    // 🔧 修复自动轮播冲突：在滚动锁定状态下不启动自动轮播
    if (isScrollLocked) {
      console.log('🚀 Auto-rotation not started - scroll is locked');
      return;
    }

    if (hasViewedAllFeatures) {
      console.log('🚀 Auto-rotation not started - all features already viewed');
      return;
    }

    // 🔧 新增：检查组件是否在视口中可见，如果可见则不启动自动轮播
    const rect = keyFeaturesSection.getBoundingClientRect();
    const isComponentVisible = rect.top < window.innerHeight && rect.bottom > 0;
    if (isComponentVisible) {
      console.log('🚀 Auto-rotation not started - component is visible in viewport');
      console.log('🚀 Component position:', { top: rect.top, bottom: rect.bottom, windowHeight: window.innerHeight });
      return;
    }

    // 强制清理现有定时器
    if (autoRotateInterval) {
      console.log('🚀 Clearing existing autoRotateInterval before starting new one');
      clearInterval(autoRotateInterval);
      autoRotateInterval = null;
    }

    console.log('🚀 Creating new autoRotateInterval');
    autoRotateInterval = setInterval(() => {
      console.log('⏰ Auto-rotation timer fired');
      console.log('⏰ Current state in timer:', { isScrollLocked, hasViewedAllFeatures, currentIndex });

      // 🔧 三重检查：执行前确认不在滚动锁定状态且组件不可见
      const rect = keyFeaturesSection.getBoundingClientRect();
      const isComponentVisible = rect.top < window.innerHeight && rect.bottom > 0;

      if (!isScrollLocked && !hasViewedAllFeatures && !isComponentVisible) {
        console.log('⏰ Executing auto feature change');
        changeFeature(currentIndex + 1);
      } else {
        console.log('⏰ Auto-rotation stopped mid-execution due to:', {
          isScrollLocked,
          hasViewedAllFeatures,
          isComponentVisible
        });
        stopAutoRotate();
      }
    }, 8000);

    console.log('🚀 Auto-rotation started successfully, interval ID:', autoRotateInterval);
  }
  
  function stopAutoRotate() {
    console.log('🛑 stopAutoRotate() called');
    console.log('🛑 Current autoRotateInterval:', autoRotateInterval);

    if (autoRotateInterval) {
      console.log('🛑 Clearing autoRotateInterval:', autoRotateInterval);
      clearInterval(autoRotateInterval);
      autoRotateInterval = null;
      console.log('🛑 autoRotateInterval cleared and set to null');
    } else {
      console.log('🛑 No autoRotateInterval to clear');
    }

    console.log('🛑 stopAutoRotate() completed, autoRotateInterval is now:', autoRotateInterval);
  }
  
  // 🔧 修复自动轮播冲突：只在非滚动锁定状态且组件不可见时启动自动轮播
  function startAutoRotateIfAllowed() {
    const rect = keyFeaturesSection.getBoundingClientRect();
    const isComponentVisible = rect.top < window.innerHeight && rect.bottom > 0;

    if (!isScrollLocked && !hasViewedAllFeatures && !isComponentVisible) {
      startAutoRotate();
      console.log('Auto-rotation started (not locked, not all viewed, not visible)');
    } else {
      console.log('Auto-rotation not started:', {
        isScrollLocked,
        hasViewedAllFeatures,
        isComponentVisible
      });
    }
  }

  // 初始启动自动轮播（如果允许）
  startAutoRotateIfAllowed();

  // Stop on user interaction
  keyFeaturesSection.addEventListener('mouseenter', stopAutoRotate);

  // 🔧 移动端优化：使用更精确的触摸事件处理
  if (!isMobileDevice()) {
    // 桌面端：直接在 touchstart 时停止自动轮播
    keyFeaturesSection.addEventListener('touchstart', stopAutoRotate, { passive: true });
  }
  // 移动端的触摸处理已在上面的滑动功能中优化处理

  // Resume when user leaves (only if allowed)
  keyFeaturesSection.addEventListener('mouseleave', startAutoRotateIfAllowed);

  // Initialize features after everything is set up
  // Wait for images to load and DOM to be ready
  function waitForImagesAndInitialize() {
    const imagePromises = Array.from(featureImages).map(img => {
      return new Promise(resolve => {
        if (img.complete && img.naturalHeight !== 0) {
          resolve();
        } else {
          img.addEventListener('load', resolve);
          img.addEventListener('error', resolve);
          // Fallback timeout
          setTimeout(resolve, 2000);
        }
      });
    });

    Promise.all(imagePromises).then(() => {
      // Small delay to ensure DOM is fully ready
      setTimeout(() => {
        initializeFeatures();
        // Force a reflow to ensure styles are applied
        keyFeaturesSection.offsetHeight;
      }, 100);
    });
  }

  // Force initialization regardless of GSAP availability
  function forceInitialization() {
    console.log('Force initialization started');

    // Ensure basic functionality works
    if (featureImages.length > 0 && featureTags.length > 0) {
      // Set initial states
      featureImages.forEach((img, i) => {
        img.classList.toggle('active', i === 0);
      });

      featureTexts.forEach((text, i) => {
        text.classList.toggle('active', i === 0);
      });

      featureTags.forEach((tag, i) => {
        tag.classList.toggle('active', i === 0);
      });

      console.log('Basic initialization completed');
    }

    // Try to initialize GSAP features if available
    if (hasGsap && hasScrollTrigger) {
      try {
        waitForImagesAndInitialize();
        console.log('GSAP initialization completed');
      } catch (error) {
        console.error('GSAP initialization failed:', error);
      }
    }
  }

  // Initialize the component with optimized scroll detection
  function initializeWithScrollDetection() {
    // First initialize the features
    initializeFeatures();

    // Then initialize scroll detection after a short delay
    setTimeout(() => {
      initializeScrollDetection();
      console.log('Scroll detection initialized successfully');
    }, 100);
  }

  // Multiple initialization attempts
  function multipleInitAttempts() {
    initializeWithScrollDetection();

    // Retry after 1 second
    setTimeout(() => {
      if (!isInitialized) {
        console.log('Retrying initialization...');
        initializeWithScrollDetection();
      }
    }, 1000);

    // Final retry after 3 seconds
    setTimeout(() => {
      if (!isInitialized) {
        console.log('Final initialization attempt...');
        initializeWithScrollDetection();
      }
    }, 3000);
  }

  // 🔧 监听导航强制解锁事件
  document.addEventListener('forceUnlockScrollLock', (e) => {
    console.log('🔓 Received force unlock request from:', e.detail.source);
    if (isScrollLocked) {
      console.log('🔓 Force unlocking ir3-v2-key-features scroll lock');
      disableScrollLock();

      // 停止自动轮播
      stopAutoRotate();

      // 重置状态以允许正常导航
      isInSection = false;
      hasViewedAllFeatures = true; // 标记为已完成，避免重新锁定
    }

    // 🔧 激活导航模式，防止导航期间重新锁定
    console.log('🚀 Activating navigation mode to prevent re-locking');
    isNavigationMode = true;

    // 清除之前的定时器
    if (navigationModeTimer) {
      clearTimeout(navigationModeTimer);
    }

    // 3秒后自动退出导航模式
    navigationModeTimer = setTimeout(() => {
      isNavigationMode = false;
      console.log('🔓 Navigation mode deactivated');
    }, 3000);
  });

  // 🔧 暴露解锁函数到全局作用域供导航组件调用
  window.disableScrollLock = disableScrollLock;

  // Start initialization with multiple attempts
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', multipleInitAttempts);
  } else {
    multipleInitAttempts();
  }

  // Also try when window loads
  window.addEventListener('load', () => {
    if (!isInitialized) {
      console.log('Window load initialization attempt...');
      initializeWithScrollDetection();
    }
  });
});
</script>

{% schema %}
{
  "name": "IR3 V2 Key Features",
  "tag": "section",
  "class": "ir3-key-features-section",
  "settings": [
    {
      "type": "header",
      "content": "Title Settings"
    },
    {
      "type": "text",
      "id": "main_title",
      "label": "Main Title",
      "default": "Key Features"
    },
    {
      "type": "textarea",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Experience three groundbreaking innovations in a single machine: endless batch production, unlimited Z-axis printing, and support-free overhang technology."
    },
    {
      "type": "header",
      "content": "Feature 1 Settings"
    },
    {
      "type": "text",
      "id": "feature_1_title",
      "label": "Feature 1 Title",
      "default": "Unlimited Batch Production"
    },
    {
      "type": "textarea",
      "id": "feature_1_description",
      "label": "Feature 1 Description",
      "default": "Repeat print the same model multiple times or print multiple models at once. Test and adjust slice settings first, then start mass production."
    },
    {
      "type": "text",
      "id": "feature_1_bullet_1",
      "label": "Feature 1 Bullet Point 1",
      "default": "Multiple models in one batch"
    },
    {
      "type": "text",
      "id": "feature_1_bullet_2",
      "label": "Feature 1 Bullet Point 2",
      "default": "Perfect for mass production"
    },
    {
      "type": "text",
      "id": "feature_1_bullet_3",
      "label": "Feature 1 Bullet Point 3",
      "default": "Consistent quality across prints"
    },
    {
      "type": "image_picker",
      "id": "feature_1_image",
      "label": "Feature 1 Image"
    },
    {
      "type": "header",
      "content": "Feature 2 Settings"
    },
    {
      "type": "text",
      "id": "feature_2_title",
      "label": "Feature 2 Title",
      "default": "Break the Z-Axis Limitation"
    },
    {
      "type": "textarea",
      "id": "feature_2_description",
      "label": "Feature 2 Description",
      "default": "Print models without length restrictions. Perfect for architectural models, long tools, and oversized prototypes."
    },
    {
      "type": "text",
      "id": "feature_2_bullet_1",
      "label": "Feature 2 Bullet Point 1",
      "default": "Unlimited Length printing"
    },
    {
      "type": "text",
      "id": "feature_2_bullet_2",
      "label": "Feature 2 Bullet Point 2",
      "default": "Perfect for architectural models"
    },
    {
      "type": "text",
      "id": "feature_2_bullet_3",
      "label": "Feature 2 Bullet Point 3",
      "default": "No Z-axis height constraints"
    },
    {
      "type": "image_picker",
      "id": "feature_2_image",
      "label": "Feature 2 Image"
    },
    {
      "type": "header",
      "content": "Feature 3 Settings"
    },
    {
      "type": "text",
      "id": "feature_3_title",
      "label": "Feature 3 Title",
      "default": "Support-Free Overhang Printing"
    },
    {
      "type": "textarea",
      "id": "feature_3_description",
      "label": "Feature 3 Description",
      "default": "Back printing angles don't need support structures, saving material and post-processing time."
    },
    {
      "type": "text",
      "id": "feature_3_bullet_1",
      "label": "Feature 3 Bullet Point 1",
      "default": "No Support Needed for overhangs"
    },
    {
      "type": "text",
      "id": "feature_3_bullet_2",
      "label": "Feature 3 Bullet Point 2",
      "default": "Saves material and post-processing time"
    },
    {
      "type": "text",
      "id": "feature_3_bullet_3",
      "label": "Feature 3 Bullet Point 3",
      "default": "Superior surface finish quality"
    },
    {
      "type": "image_picker",
      "id": "feature_3_image",
      "label": "Feature 3 Image"
    },
    {
      "type": "header",
      "content": "Feature Tags"
    },
    {
      "type": "text",
      "id": "tag_1_text",
      "label": "Tag 1 Text",
      "default": "Batch"
    },
    {
      "type": "text",
      "id": "tag_2_text",
      "label": "Tag 2 Text",
      "default": "Unlimited"
    },
    {
      "type": "text",
      "id": "tag_3_text",
      "label": "Tag 3 Text",
      "default": "Printing"
    },
    {
      "type": "text",
      "id": "tag_4_text",
      "label": "Tag 4 Text",
      "default": "Support-Free"
    },
    {
      "type": "header",
      "content": "Spacing Settings"
    },
    {
      "type": "number",
      "id": "margin_top",
      "label": "Top Margin (px)",
      "default": 0
    },
    {
      "type": "number",
      "id": "margin_bottom",
      "label": "Bottom Margin (px)",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "IR3 V2 Key Features"
    }
  ]
}
{% endschema %} 